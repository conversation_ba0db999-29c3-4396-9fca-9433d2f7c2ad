"""Configuration management for Photo Center."""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple


class Config:
    """Configuration manager for Photo Center application."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration.
        
        Args:
            config_path: Path to configuration file. If None, uses default config.yaml
        """
        if config_path is None:
            # Look for config.yaml in project root
            current_dir = Path(__file__).parent
            project_root = current_dir.parent.parent.parent
            config_path = project_root / "config.yaml"
        
        self.config_path = Path(config_path)
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        with open(self.config_path, 'r') as f:
            return yaml.safe_load(f)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation.
        
        Args:
            key: Configuration key (e.g., 'models.human_detection.confidence_threshold')
            default: Default value if key not found
            
        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value using dot notation.
        
        Args:
            key: Configuration key
            value: Value to set
        """
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self) -> None:
        """Save current configuration to file."""
        with open(self.config_path, 'w') as f:
            yaml.dump(self._config, f, default_flow_style=False, indent=2)
    
    # Convenience properties for commonly used settings
    @property
    def model_confidence_threshold(self) -> float:
        """Get model confidence threshold."""
        return self.get('models.human_detection.confidence_threshold', 0.5)
    
    @property
    def model_device(self) -> str:
        """Get model device (cpu/cuda)."""
        return self.get('models.human_detection.device', 'cpu')
    
    @property
    def output_format(self) -> str:
        """Get output image format."""
        return self.get('image_processing.output_format', 'jpg')
    
    @property
    def output_quality(self) -> int:
        """Get output image quality."""
        return self.get('image_processing.output_quality', 95)
    
    @property
    def target_position(self) -> Tuple[float, float]:
        """Get target position for centering."""
        pos = self.get('centering.target_position', [0.5, 0.4])
        return tuple(pos)
    
    @property
    def margin_ratio(self) -> float:
        """Get margin ratio for centering."""
        return self.get('centering.margin_ratio', 0.1)

    @property
    def face_weight(self) -> float:
        """Get face weight for face/chest centering."""
        return self.get('centering.face_weight', 0.7)

    @property
    def chest_weight(self) -> float:
        """Get chest weight for face/chest centering."""
        return self.get('centering.chest_weight', 0.3)

    @property
    def hip_weight(self) -> float:
        """Get hip weight for face/chest/hip centering."""
        return self.get('centering.hip_weight', 0.2)

    @property
    def supported_extensions(self) -> List[str]:
        """Get supported file extensions."""
        return self.get('batch.supported_extensions', ['.jpg', '.jpeg', '.png'])
    
    @property
    def max_workers(self) -> int:
        """Get maximum number of worker processes."""
        return self.get('batch.max_workers', 4)
    
    @property
    def log_level(self) -> str:
        """Get logging level."""
        return self.get('logging.level', 'INFO')
    
    @property
    def log_file(self) -> str:
        """Get log file path."""
        return self.get('logging.file', 'photo_center.log')
